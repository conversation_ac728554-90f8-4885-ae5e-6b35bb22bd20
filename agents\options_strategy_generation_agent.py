#!/usr/bin/env python3
"""
Options Strategy Generation Agent - Dynamic Strategy Creation & Optimization

Features:
📊 1. Strategy Generation
- Directional strategies (Long Call/Put, Covered Call)
- Volatility strategies (<PERSON>rad<PERSON>, Strangle, Iron Condor)
- Spread strategies (Bull/Bear spreads, Calendar spreads)
- Complex multi-leg strategies

📈 2. Dynamic Optimization
- Real-time strategy parameter tuning
- Market regime-based strategy selection
- Risk-adjusted strategy optimization
- Greeks-based strategy construction

⚡ 3. Strategy Validation
- Risk-reward analysis
- Probability of profit calculations
- Maximum loss/profit scenarios
- Break-even point analysis

🎯 4. Performance Optimization
- Vectorized strategy calculations
- Polars + PyArrow for fast processing
- Parallel strategy evaluation
- Memory-efficient strategy storage
"""

import asyncio
import logging
import polars as pl
import json
import aiofiles
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
import sys

# Add the project root to sys.path to allow relative imports when run directly
script_dir = Path(__file__).resolve().parent
project_root = script_dir.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# Import refactored modules
from agents.strategy_generation.config_manager import ConfigManager
from agents.strategy_generation.data_models import OptionsStrategy, StrategyType, OptionsLeg
from agents.strategy_generation.market_analyzer import MarketAnalyzer
from agents.strategy_generation.market_data_loader import MarketDataLoader
from agents.strategy_generation.strategy_calculators import StrategyCalculators
from agents.strategy_generation.strategy_generators import StrategyGenerators
from agents.strategy_generation.strategy_optimizer import StrategyOptimizer
from agents.strategy_generation.strategy_saver import StrategySaver

logger = logging.getLogger(__name__)


class OptionsStrategyGenerationAgent:
    """
    Options Strategy Generation Agent for creating and optimizing options strategies

    IMPORTANT CLARIFICATION:
    This agent generates OPTIONS STRATEGIES (actual option combinations like straddles, spreads, etc.)
    This is different from SIGNAL GENERATION STRATEGIES in config/options_strategies.yaml which define
    WHEN to trade (entry conditions, RSI levels, etc.).

    This agent generates WHAT to trade:
    - Dynamic strategy generation based on market conditions
    - Strategy parameter optimization
    - Risk-reward analysis
    - Strategy validation and filtering
    - Multi-leg strategy construction
    """

    # Define allowed strategy types (only buying/long strategies)
    ALLOWED_STRATEGY_TYPES = [
        StrategyType.LONG_CALL,
        StrategyType.LONG_PUT,
        StrategyType.PROTECTIVE_PUT,
        StrategyType.ATM_LONG_CALL,
        StrategyType.ATM_LONG_PUT,
        StrategyType.OTM_LONG_CALL,
        StrategyType.OTM_LONG_PUT,
        StrategyType.FAR_OTM_LONG_CALL,
        StrategyType.FAR_OTM_LONG_PUT,
        StrategyType.DEEP_OTM_LONG_CALL,
        StrategyType.DEEP_OTM_LONG_PUT,
        StrategyType.INTRADAY_SCALPING_CALL,
        StrategyType.INTRADAY_SCALPING_PUT,
        StrategyType.INTRADAY_MOMENTUM_CALL,
        StrategyType.INTRADAY_MOMENTUM_PUT,
        StrategyType.INTRADAY_REVERSAL_CALL,
        StrategyType.INTRADAY_REVERSAL_PUT,
        StrategyType.GAMMA_SCALPING_LONG,
        StrategyType.VOLATILITY_BREAKOUT_LONG,
        StrategyType.LONG_STRADDLE,
        StrategyType.LONG_STRANGLE,
        StrategyType.WEEKLY_EXPIRY_STRADDLE,
        StrategyType.MONTHLY_EXPIRY_STRANGLE,
    ]
    """
    The signal generation strategies from YAML are used by the signal generation agent
    to determine WHEN to execute these options strategies.
    """

    def __init__(self, config_path: str = "config/options_strategy_generation_config.yaml"):
        """Initialize Options Strategy Generation Agent"""
        self.config_path = config_path
        self.config = None
        self.is_running = False

        # Data paths
        self.data_path = Path("data")
        self.strategies_path = self.data_path / "strategies"

        # Create directories
        self.strategies_path.mkdir(parents=True, exist_ok=True)

        # Initialize refactored components
        self.config_manager = ConfigManager(config_path)
        self.market_analyzer = MarketAnalyzer()
        self.market_data_loader = MarketDataLoader(self.data_path)
        self.strategy_calculators = StrategyCalculators()
        self.strategy_saver = StrategySaver(self.strategies_path)

        # These will be initialized after config is loaded
        self.strategy_generators = None
        self.strategy_optimizer = None

        # Strategy cache
        self.generated_strategies = {}

        logger.info("[INIT] Options Strategy Generation Agent initialized")

    async def initialize(self, **kwargs):
        """Initialize the agent with optional parameters"""
        try:
            # Load configuration using config manager
            self.config = await self.config_manager.load_config()

            # Initialize components that depend on config
            self.strategy_generators = StrategyGenerators(self.config, self.strategy_calculators)
            self.strategy_optimizer = StrategyOptimizer(self.config)

            # Store kwargs for later use
            self.init_kwargs = kwargs

            # Load market data using market data loader
            self.market_data_cache = await self.market_data_loader.load_market_data(
                self.config['underlying_symbols']
            )

            logger.info("[SUCCESS] Options Strategy Generation Agent initialized successfully")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False

    async def start(self, **kwargs) -> bool:
        """Enhanced start method with comprehensive strategy generation"""
        try:
            # Get trading mode from kwargs
            trading_mode = kwargs.get('trading_mode', 'demo')
            logger.info(f"[START] Starting Enhanced Options Strategy Generation Agent in {trading_mode.upper()} mode...")

            self.is_running = True

            # Extract date parameters if provided (for training pipeline)
            from_date = kwargs.get('from_date')
            to_date = kwargs.get('to_date')

            if from_date and to_date:
                logger.info("[MODE] Training pipeline mode - generating comprehensive strategies")
                # Generate comprehensive strategies for training
                success = await self._generate_comprehensive_strategies(from_date, to_date)
                return success
            else:
                logger.info("[MODE] Live mode - generating real-time strategies")
                # Generate strategies for each underlying
                for underlying in self.config['underlying_symbols']:
                    await self._generate_strategies_for_underlying(underlying)

                # Save generated strategies
                await self._save_strategies()

                logger.info("[SUCCESS] Strategy generation completed")
                return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to start agent: {e}")
            return False

    async def _generate_comprehensive_strategies(self, from_date: str, to_date: str) -> bool:
        """Generate comprehensive strategies for training pipeline"""
        try:
            logger.info("[COMPREHENSIVE] Starting comprehensive strategy generation...")

            # Load feature data for strategy generation
            for underlying in self.config['underlying_symbols']:
                logger.info(f"[COMPREHENSIVE] Processing {underlying} strategies...")

                # Load feature data for all timeframes
                for timeframe in ["1min", "3min", "5min", "15min"]:
                    logger.info(f"[COMPREHENSIVE] Generating {timeframe} strategies for {underlying}...")

                    # Load feature data using market data loader
                    feature_data = await self.market_data_loader.load_feature_data(underlying, timeframe)

                    if feature_data is None or feature_data.height == 0:
                        logger.warning(f"[WARNING] No feature data found for {underlying} {timeframe}")
                        continue

                    # Generate comprehensive strategies including deep OTM and intraday
                    strategies = await self._generate_comprehensive_strategy_set(
                        underlying, timeframe, feature_data
                    )

                    if strategies:
                        # Save strategies using strategy saver
                        await self.strategy_saver.save_comprehensive_strategies(strategies, underlying, timeframe)
                        logger.info(f"[SUCCESS] Generated {len(strategies)} strategies for {underlying} {timeframe}")

            logger.info("[SUCCESS] Comprehensive strategy generation completed")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Comprehensive strategy generation failed: {e}")
            return False
    
    async def _generate_comprehensive_strategy_set(self, underlying: str, timeframe: str, feature_data: pl.DataFrame) -> List[OptionsStrategy]:
        """
        Generates a comprehensive set of strategies by calling all relevant generation methods.
        This method orchestrates the calls to individual strategy generation methods.
        """
        all_strategies = []
        
        # Market conditions would typically be derived from feature_data or a separate analysis
        # Assuming MarketAnalyzer has an analyze_market_conditions method
        market_conditions = self.market_analyzer.analyze_market_conditions(feature_data) 

        # Call individual strategy generation methods via StrategyGenerators
        # Note: The actual generation logic for each type is within StrategyGenerators
        # This method orchestrates which types to generate based on context (e.g., comprehensive set)
        
        # Example calls (these methods are now expected to be in StrategyGenerators)
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.DEEP_OTM_LONG_CALL, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.DEEP_OTM_LONG_PUT, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.INTRADAY_SCALPING_CALL, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.INTRADAY_SCALPING_PUT, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.INTRADAY_MOMENTUM_CALL, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.INTRADAY_MOMENTUM_PUT, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.INTRADAY_REVERSAL_CALL, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.INTRADAY_REVERSAL_PUT, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.GAMMA_SCALPING_LONG, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.VOLATILITY_BREAKOUT_LONG, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.LONG_STRADDLE, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.LONG_STRANGLE, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.WEEKLY_EXPIRY_STRADDLE, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.MONTHLY_EXPIRY_STRANGLE, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.CALL_CALENDAR_SPREAD, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.PUT_CALENDAR_SPREAD, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.VIX_BASED_STRATEGY, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.ATM_LONG_CALL, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.ATM_LONG_PUT, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.OTM_LONG_CALL, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.OTM_LONG_PUT, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.FAR_OTM_LONG_CALL, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.FAR_OTM_LONG_PUT, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.DEEP_OTM_LONG_CALL, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.DEEP_OTM_LONG_PUT, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.LONG_CALL, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.LONG_PUT, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))
        all_strategies.extend(await self.strategy_generators.generate_strategy_type(
            StrategyType.PROTECTIVE_PUT, underlying, self.market_data_cache[underlying], self._estimate_spot_price(self.market_data_cache[underlying])
        ))


        # Filter and optimize the combined strategies
        filtered_strategies = await self.strategy_optimizer.filter_strategies(all_strategies)
        optimized_strategies = await self.strategy_optimizer.optimize_strategies(filtered_strategies)

        return optimized_strategies

    async def _generate_strategies_for_underlying(self, underlying: str):
        """Generate strategies for specific underlying"""
        try:
            logger.info(f"[GENERATE] Generating strategies for {underlying}...")

            if underlying not in self.market_data_cache:
                logger.warning(f"[WARNING] No market data for {underlying}")
                return

            option_chain = self.market_data_cache[underlying]

            # Get current spot price (estimated from ATM options)
            spot_price = self._estimate_spot_price(option_chain)

            strategies = []

            # Generate each allowed buying/long strategy type using strategy generators
            for strategy_type in self.ALLOWED_STRATEGY_TYPES:
                strategy_list = await self.strategy_generators.generate_strategy_type(
                    strategy_type, underlying, option_chain, spot_price
                )
                strategies.extend(strategy_list)

            # Filter and optimize strategies using strategy optimizer
            filtered_strategies = await self.strategy_optimizer.filter_strategies(strategies)
            optimized_strategies = await self.strategy_optimizer.optimize_strategies(filtered_strategies)

            # Store strategies
            self.generated_strategies[underlying] = optimized_strategies

            logger.info(f"[SUCCESS] Generated {len(optimized_strategies)} strategies for {underlying}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate strategies for {underlying}: {e}")
    
    def _estimate_spot_price(self, option_chain: pl.DataFrame) -> float:
        """Estimate current spot price from option chain"""
        try:
            # Find ATM options (closest to spot)
            strikes = option_chain['strike_price'].unique().sort().to_list()

            if not strikes:
                return 25000.0  # Default for Nifty

            # Use middle strike as approximation
            mid_strike = strikes[len(strikes) // 2]
            return mid_strike

        except Exception as e:
            logger.warning(f"[WARNING] Failed to estimate spot price: {e}")
            return 25000.0

    async def _save_strategies(self):
        """Save generated strategies using StrategySaver"""
        try:
            logger.info("[SAVE] Saving generated strategies...")

            all_strategies = []
            for underlying, strategies in self.generated_strategies.items():
                all_strategies.extend(strategies)

            if all_strategies:
                # Use strategy saver to save strategies
                await self.strategy_saver.save_strategies(all_strategies)
                logger.info(f"[SUCCESS] Saved {len(all_strategies)} strategies")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save strategies: {e}")

    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("[CLEANUP] Cleaning up Options Strategy Generation Agent...")
            self.is_running = False
            self.generated_strategies.clear()
            self.market_data_cache.clear()
            logger.info("[SUCCESS] Options Strategy Generation Agent cleaned up")

        except Exception as e:
            logger.error(f"[ERROR] Failed to cleanup agent: {e}")


if __name__ == "__main__":
    # Example usage
    async def main():
        """Example usage of Options Strategy Generation Agent"""
        agent = OptionsStrategyGenerationAgent()
        
        try:
            await agent.initialize()
            await agent.start()
        except KeyboardInterrupt:
            logger.info("Agent interrupted by user")
        finally:
            await agent.cleanup()

    asyncio.run(main())
